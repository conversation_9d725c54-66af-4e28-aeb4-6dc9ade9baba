<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Customer;
use Illuminate\Support\Facades\Auth;

class AgenController extends Controller
{
    public function index(Request $request)
    {
        $agen = Auth::user()->id;
        $query = Customer::with(['invoice.status', 'paket'])
            ->where('agen_id', $agen)
            ->where('status_id', 3);

        // Apply search filter if provided
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('nama_customer', 'LIKE', "%{$search}%")
                  ->orWhere('alamat', 'LIKE', "%{$search}%")
                  ->orWhere('no_hp', 'LIKE', "%{$search}%");
            });
        }

        // Apply status filter if provided
        if ($request->has('status_tagihan') && !empty($request->status_tagihan)) {
            $status = $request->status_tagihan;
            if ($status === 'Sudah Bayar') {
                $query->whereHas('invoice.status', function($q) {
                    $q->where('nama_status', 'Sudah Bayar');
                });
            } elseif ($status === 'Belum Bayar') {
                $query->where(function($q) {
                    $q->whereHas('invoice.status', function($subQ) {
                        $subQ->where('nama_status', '!=', 'Sudah Bayar');
                    })->orWhereDoesntHave('invoice.status');
                });
            }
        }

        $customers = $query->paginate(10);

        // If this is an AJAX request, return JSON
        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'data' => $customers,
                'html' => view('agen.partials.customer-table-rows', compact('customers'))->render()
            ]);
        }

        return view('agen.data-pelanggan-agen',[
            'users' => Auth::user(),
            'roles' => Auth::user()->roles,
            'customers' => $customers,
        ]);
    }

    public function search(Request $request)
    {
        $agen = Auth::user()->id;
        $query = Customer::with(['invoice.status', 'paket'])
            ->where('agen_id', $agen)
            ->where('status_id', 3);

        // Apply search filter
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('nama_customer', 'LIKE', "%{$search}%")
                  ->orWhere('alamat', 'LIKE', "%{$search}%")
                  ->orWhere('no_hp', 'LIKE', "%{$search}%");
            });
        }

        // Apply status filter
        if ($request->has('status_tagihan') && !empty($request->status_tagihan)) {
            $status = $request->status_tagihan;
            if ($status === 'Sudah Bayar') {
                $query->whereHas('invoice.status', function($q) {
                    $q->where('nama_status', 'Sudah Bayar');
                });
            } elseif ($status === 'Belum Bayar') {
                $query->where(function($q) {
                    $q->whereHas('invoice.status', function($subQ) {
                        $subQ->where('nama_status', '!=', 'Sudah Bayar');
                    })->orWhereDoesntHave('invoice.status');
                });
            }
        }

        $customers = $query->paginate(10);

        return response()->json([
            'success' => true,
            'data' => $customers->items(),
            'pagination' => [
                'current_page' => $customers->currentPage(),
                'last_page' => $customers->lastPage(),
                'per_page' => $customers->perPage(),
                'total' => $customers->total(),
            ]
        ]);
    }
}
