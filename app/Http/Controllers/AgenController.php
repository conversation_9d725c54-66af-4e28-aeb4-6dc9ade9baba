<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Customer;
use App\Models\Invoice;

class AgenController extends Controller
{
    public function index()
    {
        $agen = auth()->user()->id;
        $customers = Customer::with('invoice')
            ->where('agen_id', $agen)
            ->where('status_id', 3)
            ->paginate(10);
            
        
        $coba = Invoice::with('customer','status')->whereIn('customer_id', $customers->pluck('id'))->get();
        
        return view('agen.data-pelanggan-agen',[
            'users' => auth()->user(),
            'roles' => auth()->user()->roles,
            'customers' => $customers,
        ]);
    }
}
