@extends('layouts.contentNavbarLayout')
@section('title', 'Data Pelanggan Agen')

@section('content')
<div class="row">
    <div class="col-sm-12">
        <div class="card mb-2">
            <div class="card-header modern-card-header">
                <h4 class="card-title fw-bold">Data Pelanggan</h4>
                <small class="card-subtitle text-muted">Daftar seluruh pelanggan yang terdaftar</small>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <div class="row mb-5">
                    <div class="col-sm-4 mb-2">
                        <div class="row">
                            <div class="col-sm-12">
                                <label class="form-label">Nama Pelanggan</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" placeholder="Cari pelanggan..."
                                    aria-label="Cari pelanggan..." aria-describedby="button-addon2" id="searchCustomer">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-4 mb-2">
                        <div class="row">
                            <div class="col-sm-12">
                                <label class="form-label">Status Tagihan</label>
                                <select name="" id="" class="form-select">
                                    <option value="" selected disabled>Pilih Status</option>
                                    <option value="">Belum Bayar</option>
                                    <option value="">Sudah Bayar</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    @php
                        $bulan = [
                            'Januari', 'Februari', 'Maret', 'April',
                            'Mei', 'Juni', 'Juli', 'Agustus',
                            'September', 'Oktober', 'November', 'Desember'
                            ];
                    @endphp
                    
                    <div class="col-sm-4 mb-2">
                        <div class="row">
                            <div class="col-sm-12">
                                <label class="form-label">Periode Bulan</label>
                                <select name="bulan" id="bulan" class="form-select">
                                    <option value="" selected disabled>Pilih Bulan</option>
                                    @foreach ($bulan as $item)
                                    <option value="{{ $item }}">{{ $item }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <hr class="my-2 mb-5">
                <div class="table-responsive mb-2">
                    <table class="table modern-table" id="customerTable">
                        <thead class="table-dark text-center fw-bold">
                            <tr>
                                <th>No</th>
                                <th>Nama</th>
                                <th>Alamat</th>
                                <th>Telp.</th>
                                <th>Paket</th>
                                <th>Status Tagihan</th>
                                <th>Jatuh Tempo</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody class="text-center">
                            @forelse ($customers as $item)
                            <tr class="customer-row text-center" data-id="{{ $item->id }}"
                                data-tagihan="{{ $item->invoice->isNotEmpty() && $item->invoice->first()->status ? ($item->invoice->first()->status->nama_status == 'Sudah Bayar' ? '0' : $item->invoice->first()->tagihan ?? '0') : '0' }}"
                                data-customer-id="{{ $item->id }}"
                                data-invoice-id="{{ $item->invoice->isNotEmpty() ? $item->invoice->first()->id : '' }}"
                                data-tagihan-tambahan="{{ $item->invoice->isNotEmpty() ? $item->invoice->first()->tambahan : '' }}">
                                <td class="text-center">{{ $loop->iteration }}</td>
                                <td class="customer-name">{{ $item->nama_customer }}</td>
                                <td class="customer-address">{{ $item->alamat }}</td>
                                <td class="nomor-hp">{{ $item->no_hp }}</td>
                                <td>
                                    <span class="badge bg-warning bg-opacity-10 status-badge text-warning">
                                        {{ $item->paket->nama_paket }}
                                    </span>
                                    @if($item->status_id == 3)
                                    <small class="badge bg-success bg-opacity-10 text-success mt-3">Aktif</small>
                                    @elseif($item->status_id == 9)
                                    <small class="badge bg-danger bg-opacity-10 text-danger mt-3">Non Aktif</small>
                                    @endif
                                </td>
                                <td class="text-center">
                                    @if ($item->invoice->isNotEmpty() && $item->invoice->first()->status)
                                    <span class="badge 
                                    bg-{{ $item->invoice->first()->status->nama_status == 'Sudah Bayar' ? 'success' : 'danger' }} 
                                    bg-opacity-10 
                                    {{ $item->invoice->first()->status->nama_status == 'Sudah Bayar' ? 'text-success' : 'text-danger' }} 
                                    status-badge">
                                    {{ $item->invoice->first()->status->nama_status }}
                                </span>
                                @else
                                <span class="badge bg-secondary bg-opacity-10 text-secondary status-badge">N/A</span>
                                @endif
                            </td>
                            <td>
                                @if ($item->invoice->isNotEmpty() && $item->invoice->first()->status)
                                <span class="badge bg-danger bg-opacity-10 text-danger">
                                    {{ \Carbon\Carbon::parse($item->invoice->first()->jatuh_tempo)->format('d M Y') }}
                                </span>
                                @else
                                <span class="badge bg-secondary bg-opacity-10 text-secondary">N/A</span>
                                @endif
                            </td>
                            <td>
                                <a href="/detail-pelanggan/{{ $item->id }}"
                                    class="btn btn-outline-info btn-sm mb-2"
                                    data-bs-toggle="tooltip" data-bs-placement="bottom"
                                    title="Detail Pelanggan">
                                    <i class="bx bx-show"></i>
                                </a>
                                <a href="/payment/invoice/{{ $item->invoice->first()->id }}"
                                    class="btn btn-outline-success btn-sm mb-2"
                                    data-bs-toggle="tooltip" data-bs-placement="bottom"
                                    title="Bayar Tagihan">
                                    <i class="bx bx-money"></i>
                                </a>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="8" class="text-center py-5">
                                <div class="d-flex flex-column align-items-center">
                                    <i class="bx bx-receipt text-muted" style="font-size: 3rem;"></i>
                                    <h5 class="text-dark mt-3 mb-2">Tidak ada data</h5>
                                    <p class="text-muted mb-0">Belum ada Data Pelanggan</p>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
            <div class="d-flex justify-content-center">
                {{ $customers->links() }}
            </div>
        </div>
    </div>
</div>
</div>
@endsection