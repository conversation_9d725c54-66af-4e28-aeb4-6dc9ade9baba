@extends('layouts.contentNavbarLayout')
@section('title', 'Data Pelanggan Agen')

@section('page-style')
<style>
.search-highlight {
    background-color: #fff3cd;
    padding: 2px 4px;
    border-radius: 3px;
}

.modern-card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 0.5rem 0.5rem 0 0;
}

.modern-table {
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
}

.modern-table thead th {
    background: #343a40;
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

.modern-table tbody tr {
    transition: all 0.3s ease;
}

.modern-table tbody tr:hover {
    background-color: #f8f9fa;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.status-badge {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.5rem 0.75rem;
    border-radius: 0.5rem;
}

.search-container {
    background: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border: 1px solid #e9ecef;
}

.input-group .btn {
    border-color: #ced4da;
}

.input-group .input-group-text {
    background-color: #e9ecef;
    border-color: #ced4da;
    color: #6c757d;
}
</style>
@endsection

@section('content')
<div class="row">
    <div class="col-sm-12">
        <div class="card mb-2">
            <div class="card-header modern-card-header">
                <h4 class="card-title fw-bold">Data Pelanggan</h4>
                <small class="card-subtitle text-muted">Daftar seluruh pelanggan yang terdaftar</small>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <div class="search-container">
                    <h6 class="mb-3 fw-bold text-dark">
                        <i class="bx bx-search me-2"></i>Filter & Pencarian Data
                    </h6>
                <div class="row mb-3">
                    <div class="col-sm-4 mb-2">
                        <div class="row">
                            <div class="col-sm-12">
                                <label class="form-label">Nama Pelanggan</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" placeholder="Cari pelanggan..."
                                    aria-label="Cari pelanggan..." aria-describedby="button-addon2" id="searchCustomer">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-4 mb-2">
                        <div class="row">
                            <div class="col-sm-12">
                                <label class="form-label">Status Tagihan</label>
                                <select name="status_tagihan" id="statusTagihan" class="form-select">
                                    <option value="" selected>Semua Status</option>
                                    <option value="Belum Bayar">Belum Bayar</option>
                                    <option value="Sudah Bayar">Sudah Bayar</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    @php
                        $bulan = [
                            'Januari', 'Februari', 'Maret', 'April',
                            'Mei', 'Juni', 'Juli', 'Agustus',
                            'September', 'Oktober', 'November', 'Desember'
                            ];
                    @endphp
                    <div class="col-sm-4 mb-2">
                        <div class="row">
                            <div class="col-sm-12">
                                <label class="form-label">Periode Bulan</label>
                                <select name="bulan" id="bulan" class="form-select">
                                    <option value="" selected disabled>Pilih Bulan</option>
                                    @foreach ($bulan as $item)
                                    <option value="{{ $item }}">{{ $item }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                </div>
                <hr class="my-2 mb-4">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <span class="text-muted" id="searchResults">
                            Menampilkan <span class="fw-bold" id="visibleCount">{{ $customers->count() }}</span>
                            dari <span class="fw-bold" id="totalCount">{{ $customers->count() }}</span> data
                        </span>
                    </div>
                    <div>
                        <button type="button" class="btn btn-outline-secondary btn-sm" id="resetFilters">
                            <i class="bx bx-refresh me-1"></i>Reset Filter
                        </button>
                    </div>
                </div>
                <div class="table-responsive mb-2">
                    <table class="table modern-table" id="customerTable">
                        <thead class="table-dark text-center fw-bold">
                            <tr>
                                <th>No</th>
                                <th>Nama</th>
                                <th>Alamat</th>
                                <th>Telp.</th>
                                <th>Paket</th>
                                <th>Status Tagihan</th>
                                <th>Jatuh Tempo</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody class="text-center">
                            @forelse ($customers as $item)
                            <tr class="customer-row text-center" data-id="{{ $item->id }}"
                                data-tagihan="{{ $item->invoice->isNotEmpty() && $item->invoice->first()->status ? ($item->invoice->first()->status->nama_status == 'Sudah Bayar' ? '0' : $item->invoice->first()->tagihan ?? '0') : '0' }}"
                                data-customer-id="{{ $item->id }}"
                                data-invoice-id="{{ $item->invoice->isNotEmpty() ? $item->invoice->first()->id : '' }}"
                                data-tagihan-tambahan="{{ $item->invoice->isNotEmpty() ? $item->invoice->first()->tambahan : '' }}">
                                <td class="text-center">{{ $loop->iteration }}</td>
                                <td class="customer-name">{{ $item->nama_customer }}</td>
                                <td class="customer-address">{{ $item->alamat }}</td>
                                <td class="nomor-hp">{{ $item->no_hp }}</td>
                                <td>
                                    <span class="badge bg-warning bg-opacity-10 status-badge text-warning">
                                        {{ $item->paket->nama_paket }}
                                    </span>
                                    @if($item->status_id == 3)
                                    <small class="badge bg-success bg-opacity-10 text-success mt-3">Aktif</small>
                                    @elseif($item->status_id == 9)
                                    <small class="badge bg-danger bg-opacity-10 text-danger mt-3">Non Aktif</small>
                                    @endif
                                </td>
                                <td class="text-center">
                                    @if ($item->invoice->isNotEmpty() && $item->invoice->first()->status)
                                    <span class="badge 
                                    bg-{{ $item->invoice->first()->status->nama_status == 'Sudah Bayar' ? 'success' : 'danger' }} 
                                    bg-opacity-10 
                                    {{ $item->invoice->first()->status->nama_status == 'Sudah Bayar' ? 'text-success' : 'text-danger' }} 
                                    status-badge">
                                    {{ $item->invoice->first()->status->nama_status }}
                                </span>
                                @else
                                <span class="badge bg-secondary bg-opacity-10 text-secondary status-badge">N/A</span>
                                @endif
                            </td>
                            <td>
                                @if ($item->invoice->isNotEmpty() && $item->invoice->first()->status)
                                <span class="badge bg-danger bg-opacity-10 text-danger">
                                    {{ \Carbon\Carbon::parse($item->invoice->first()->jatuh_tempo)->format('d M Y') }}
                                </span>
                                @else
                                <span class="badge bg-secondary bg-opacity-10 text-secondary">N/A</span>
                                @endif
                            </td>
                            <td>
                                <a href="/detail-pelanggan/{{ $item->id }}"
                                    class="btn btn-outline-info btn-sm mb-2"
                                    data-bs-toggle="tooltip" data-bs-placement="bottom"
                                    title="Detail Pelanggan">
                                    <i class="bx bx-show"></i>
                                </a>
                                <a href="/payment/invoice/{{ $item->invoice->first()->id }}"
                                    class="btn btn-outline-success btn-sm mb-2"
                                    data-bs-toggle="tooltip" data-bs-placement="bottom"
                                    title="Bayar Tagihan">
                                    <i class="bx bx-money"></i>
                                </a>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="8" class="text-center py-5">
                                <div class="d-flex flex-column align-items-center">
                                    <i class="bx bx-receipt text-muted" style="font-size: 3rem;"></i>
                                    <h5 class="text-dark mt-3 mb-2">Tidak ada data</h5>
                                    <p class="text-muted mb-0">Belum ada Data Pelanggan</p>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
            <div class="d-flex justify-content-center">
                {{ $customers->links() }}
            </div>
        </div>
    </div>
</div>
</div>
@endsection

@section('page-script')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchCustomer');
    const statusSelect = document.getElementById('statusTagihan');
    const bulanSelect = document.getElementById('bulan');
    const customerTable = document.getElementById('customerTable');
    const customerRows = customerTable.querySelectorAll('.customer-row');

    // Function to filter table rows
    function filterTable() {
        const searchTerm = searchInput.value.toLowerCase().trim();
        const selectedStatus = statusSelect.value;
        const selectedBulan = bulanSelect.value;

        customerRows.forEach(function(row) {
            const customerName = row.querySelector('.customer-name').textContent.toLowerCase();
            const customerAddress = row.querySelector('.customer-address').textContent.toLowerCase();
            const customerPhone = row.querySelector('.nomor-hp').textContent.toLowerCase();

            // Get status badge text
            const statusBadge = row.querySelector('.status-badge');
            const statusText = statusBadge ? statusBadge.textContent.trim() : '';

            // Check search term match (name, address, or phone)
            const matchesSearch = searchTerm === '' ||
                customerName.includes(searchTerm) ||
                customerAddress.includes(searchTerm) ||
                customerPhone.includes(searchTerm);

            // Check status filter
            let matchesStatus = true;
            if (selectedStatus && selectedStatus !== '') {
                if (selectedStatus === 'Belum Bayar') {
                    matchesStatus = !statusText.includes('Sudah Bayar') || statusText.includes('N/A');
                } else if (selectedStatus === 'Sudah Bayar') {
                    matchesStatus = statusText.includes('Sudah Bayar');
                }
            }

            // Show/hide row based on all filters
            if (matchesSearch && matchesStatus) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });

        // Update row numbers for visible rows
        updateRowNumbers();

        // Update search results counter
        updateSearchCounter();

        // Show/hide empty state
        toggleEmptyState();
    }

    // Function to update row numbers for visible rows
    function updateRowNumbers() {
        const visibleRows = Array.from(customerRows).filter(row => row.style.display !== 'none');
        visibleRows.forEach(function(row, index) {
            const numberCell = row.querySelector('td:first-child');
            if (numberCell) {
                numberCell.textContent = index + 1;
            }
        });
    }

    // Function to update search results counter
    function updateSearchCounter() {
        const visibleRows = Array.from(customerRows).filter(row => row.style.display !== 'none');
        const totalRows = customerRows.length;

        document.getElementById('visibleCount').textContent = visibleRows.length;
        document.getElementById('totalCount').textContent = totalRows;
    }

    // Function to show/hide empty state
    function toggleEmptyState() {
        const visibleRows = Array.from(customerRows).filter(row => row.style.display !== 'none');
        const tbody = customerTable.querySelector('tbody');
        let emptyRow = tbody.querySelector('.empty-state-row');

        if (visibleRows.length === 0) {
            // Hide all customer rows
            customerRows.forEach(row => row.style.display = 'none');

            // Show empty state if not exists
            if (!emptyRow) {
                emptyRow = document.createElement('tr');
                emptyRow.className = 'empty-state-row';
                emptyRow.innerHTML = `
                    <td colspan="8" class="text-center py-5">
                        <div class="d-flex flex-column align-items-center">
                            <i class="bx bx-search text-muted" style="font-size: 3rem;"></i>
                            <h5 class="text-dark mt-3 mb-2">Tidak ada hasil</h5>
                            <p class="text-muted mb-0">Tidak ditemukan data yang sesuai dengan pencarian</p>
                        </div>
                    </td>
                `;
                tbody.appendChild(emptyRow);
            }
            emptyRow.style.display = '';
        } else {
            // Hide empty state
            if (emptyRow) {
                emptyRow.style.display = 'none';
            }
        }
    }

    // Function to reset all filters
    function resetAllFilters() {
        searchInput.value = '';
        statusSelect.value = '';
        bulanSelect.value = '';
        filterTable();
        searchInput.focus();
    }

    // Add event listeners
    searchInput.addEventListener('input', function() {
        filterTable();
    });

    statusSelect.addEventListener('change', function() {
        filterTable();
    });

    bulanSelect.addEventListener('change', function() {
        filterTable();
    });

    // Reset filters button
    document.getElementById('resetFilters').addEventListener('click', function() {
        resetAllFilters();
    });

    // Add clear search functionality
    searchInput.addEventListener('keyup', function(e) {
        if (e.key === 'Escape') {
            this.value = '';
            filterTable();
        }
    });

    // Add search icon and clear button to search input
    const searchContainer = searchInput.parentElement;
    if (searchContainer.classList.contains('input-group')) {
        // Add search icon
        const searchIcon = document.createElement('span');
        searchIcon.className = 'input-group-text';
        searchIcon.innerHTML = '<i class="bx bx-search"></i>';
        searchContainer.insertBefore(searchIcon, searchInput);

        // Add clear button
        const clearButton = document.createElement('button');
        clearButton.className = 'btn btn-outline-secondary';
        clearButton.type = 'button';
        clearButton.innerHTML = '<i class="bx bx-x"></i>';
        clearButton.addEventListener('click', function() {
            searchInput.value = '';
            filterTable();
            searchInput.focus();
        });
        searchContainer.appendChild(clearButton);
    }

    // Initialize counter on page load
    updateSearchCounter();
});
</script>
@endsection