@extends('layouts.contentNavbarLayout')

@section('title', 'Data Agen')

@section('content')

<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item">
            <a href="/dashboard" class="text-decoration-none">Dashboard</a>
        </li>
        <li class="breadcrumb-item active">
            <a href="/corp/pendapatan" class="text-decoration-none"><PERSON><PERSON><PERSON></a>
        </li>
        <li class="breadcrumb-item active">
            <a href="/data/pendapatan" class="text-decoration-none">Personal</a>
        </li>
        <li class="breadcrumb-item active">Data Agen</li>
    </ol>
</nav>

<div class="row">
    <div class="col-12">
        <div class="card mb-3">
            <div class="card-header">
                <h5 class="card-title fw-bold">Data Agen atau Sales</h5>
                <small class="card-subtitle text-muted">Daftar Agen atau Sales</small>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-sm-4">
                        <label class="form-label mb-2">Nama Agen</label>
                        <div class="input-group">
                            <input type="text" class="form-control" placeholder="Cari nama agen...">
                        </div>
                    </div>
                </div>
                <hr>
                <div class="table-responsive">
                    <table class="table">
                        <thead class="table-dark text-center fw-bold">
                            <tr>
                                <th>Nama Agen</th>
                                <th>Email Agen</th>
                                <th>Alamat Agen</th>
                                <th>No. HP Agen</th>
                                <th>Jumlah Pelanggan</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody class="text-center">
                            @forelse ($agen as $a)
                                <tr>
                                    <td>{{ $a->name }}</td>
                                    <td>
                                        <span class="badge bg-warning bg-opacity-10 text-warning">
                                            {{ $a->email }}
                                        </span>
                                    </td>
                                    <td>{{ $a->alamat }}</td>
                                    <td>{{ $a->no_hp }}</td>
                                    <td>{{ $a->customer_count }}</td>
                                    <td>
                                        <a href="/agen/data-pelanggan?agen_id={{ $a->id }}" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Lihat Daftar Pelanggan">
                                            <i class="bx bx-list-ul"></i>
                                        </a>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="6" class="text-center">Tidak ada data</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
                <div class="d-flex justify-content-center">
                    {{ $agen->links() }}
                </div>
            </div>
        </div>
    </div>
</div>
@endsection