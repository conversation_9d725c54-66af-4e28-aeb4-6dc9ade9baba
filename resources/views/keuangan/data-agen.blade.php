@extends('layouts.contentNavbarLayout')

@section('title', 'Data Agen')

@section('page-style')
<style>
.search-container {
    background: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border: 1px solid #e9ecef;
}

.modern-table {
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
}

.modern-table thead th {
    background: #343a40;
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

.modern-table tbody tr {
    transition: all 0.3s ease;
}

.modern-table tbody tr:hover {
    background-color: #f8f9fa;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.input-group .btn {
    border-color: #ced4da;
}

.input-group .input-group-text {
    background-color: #e9ecef;
    border-color: #ced4da;
    color: #6c757d;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.filter-active {
    border-color: #667eea !important;
    background-color: #f8f9ff !important;
}

#filterIndicator {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}
</style>
@endsection

@section('content')

<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item">
            <a href="/dashboard" class="text-decoration-none">Dashboard</a>
        </li>
        <li class="breadcrumb-item active">
            <a href="/corp/pendapatan" class="text-decoration-none">Langganan</a>
        </li>
        <li class="breadcrumb-item active">
            <a href="/data/pendapatan" class="text-decoration-none">Personal</a>
        </li>
        <li class="breadcrumb-item active">Data Agen</li>
    </ol>
</nav>

<div class="row">
    <div class="col-12">
        <div class="card mb-3">
            <div class="card-header">
                <h5 class="card-title fw-bold">Data Agen atau Sales</h5>
                <small class="card-subtitle text-muted">Daftar Agen atau Sales</small>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <div class="search-container">
                    <h6 class="mb-3 fw-bold text-dark">
                        <i class="bx bx-search me-2"></i>Filter & Pencarian Data
                        <small class="text-muted fw-normal">
                            ({{ $agen->total() }} total agen)
                        </small>
                    </h6>
                    <div class="row mb-3">
                        <div class="col-sm-6">
                            <label class="form-label">Nama Agen</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="bx bx-search"></i>
                                </span>
                                <input type="text" class="form-control" placeholder="Cari nama, email, alamat, atau nomor HP..."
                                       id="searchAgen" title="Ketik untuk mencari berdasarkan nama, email, alamat, atau nomor HP">
                                <button class="btn btn-outline-secondary" type="button" id="clearSearch">
                                    <i class="bx bx-x"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-sm-6 d-flex align-items-end">
                            <button type="button" class="btn btn-outline-secondary btn-sm me-2" id="resetFilters">
                                <i class="bx bx-refresh me-1"></i>Reset Filter
                            </button>
                            <span class="badge bg-info" id="filterIndicator" style="display: none;">
                                <i class="bx bx-filter-alt me-1"></i>Filter Aktif
                            </span>
                        </div>
                    </div>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <span class="text-muted" id="searchResults">
                            Menampilkan <span class="fw-bold text-primary" id="visibleCount">{{ $agen->count() }}</span>
                            dari <span class="fw-bold" id="totalCount">{{ $agen->count() }}</span> data
                        </span>
                    </div>
                </div>
                <hr class="my-2 mb-4">
                <div class="table-responsive">
                    <table class="table modern-table" id="agenTable">
                        <thead class="table-dark text-center fw-bold">
                            <tr>
                                <th>No</th>
                                <th>Nama Agen</th>
                                <th>Email Agen</th>
                                <th>Alamat Agen</th>
                                <th>No. HP Agen</th>
                                <th>Jumlah Pelanggan</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody class="text-center">
                            @forelse ($agen as $a)
                                <tr class="agen-row" data-id="{{ $a->id }}"
                                    data-nama="{{ strtolower($a->name) }}"
                                    data-email="{{ strtolower($a->email) }}"
                                    data-alamat="{{ strtolower($a->alamat ?? '') }}"
                                    data-hp="{{ $a->no_hp ?? '' }}">
                                    <td class="text-center">{{ $loop->iteration }}</td>
                                    <td class="agen-name">{{ $a->name }}</td>
                                    <td>
                                        <span class="badge bg-warning bg-opacity-10 text-warning agen-email">
                                            {{ $a->email }}
                                        </span>
                                    </td>
                                    <td class="agen-alamat">{{ $a->alamat ?? '-' }}</td>
                                    <td class="agen-hp">{{ $a->no_hp ?? '-' }}</td>
                                    <td>
                                        <span class="badge bg-info bg-opacity-10 text-info">
                                            {{ $a->customer_count }} Pelanggan
                                        </span>
                                    </td>
                                    <td>
                                        <a href="/agen/data-pelanggan?agen_id={{ $a->id }}"
                                           class="btn btn-sm btn-primary"
                                           data-bs-toggle="tooltip"
                                           data-bs-placement="bottom"
                                           title="Lihat Daftar Pelanggan">
                                            <i class="bx bx-list-ul"></i>
                                        </a>
                                    </td>
                                </tr>
                            @empty
                                <tr class="empty-state-row">
                                    <td colspan="7" class="text-center py-5">
                                        <div class="d-flex flex-column align-items-center">
                                            <i class="bx bx-user-x text-muted" style="font-size: 3rem;"></i>
                                            <h5 class="text-dark mt-3 mb-2">Tidak ada data</h5>
                                            <p class="text-muted mb-0">Belum ada data agen yang terdaftar</p>
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
                <div class="d-flex justify-content-center">
                    {{ $agen->links() }}
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('page-script')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchAgen');
    const clearButton = document.getElementById('clearSearch');
    const resetButton = document.getElementById('resetFilters');
    const agenTable = document.getElementById('agenTable');
    const agenRows = agenTable.querySelectorAll('.agen-row');
    const filterIndicator = document.getElementById('filterIndicator');

    // Function to filter table rows
    function filterTable() {
        const searchTerm = searchInput.value.toLowerCase().trim();

        agenRows.forEach(function(row, index) {
            const nama = row.getAttribute('data-nama') || '';
            const email = row.getAttribute('data-email') || '';
            const alamat = row.getAttribute('data-alamat') || '';
            const hp = row.getAttribute('data-hp') || '';

            // Check search term match (nama, email, alamat, or hp)
            const matchesSearch = searchTerm === '' ||
                nama.includes(searchTerm) ||
                email.includes(searchTerm) ||
                alamat.includes(searchTerm) ||
                hp.includes(searchTerm);

            // Show/hide row based on search
            if (matchesSearch) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });

        // Update row numbers for visible rows
        updateRowNumbers();

        // Update search results counter
        updateSearchCounter();

        // Show/hide empty state
        toggleEmptyState();
    }

    // Function to update row numbers for visible rows
    function updateRowNumbers() {
        const visibleRows = Array.from(agenRows).filter(row => row.style.display !== 'none');
        visibleRows.forEach(function(row, index) {
            const numberCell = row.querySelector('td:first-child');
            if (numberCell) {
                numberCell.textContent = index + 1;
            }
        });
    }

    // Function to update search results counter
    function updateSearchCounter() {
        const visibleRows = Array.from(agenRows).filter(row => row.style.display !== 'none');
        const totalRows = agenRows.length;

        document.getElementById('visibleCount').textContent = visibleRows.length;
        document.getElementById('totalCount').textContent = totalRows;

        // Show/hide filter indicator
        const hasActiveFilters = searchInput.value.trim() !== '';

        if (hasActiveFilters) {
            filterIndicator.style.display = 'inline-block';
            searchInput.classList.add('filter-active');
        } else {
            filterIndicator.style.display = 'none';
            searchInput.classList.remove('filter-active');
        }
    }

    // Function to show/hide empty state
    function toggleEmptyState() {
        const visibleRows = Array.from(agenRows).filter(row => row.style.display !== 'none');
        const tbody = agenTable.querySelector('tbody');
        let emptyRow = tbody.querySelector('.search-empty-state');

        if (visibleRows.length === 0 && searchInput.value.trim() !== '') {
            // Hide all agen rows
            agenRows.forEach(row => row.style.display = 'none');

            // Show search empty state if not exists
            if (!emptyRow) {
                emptyRow = document.createElement('tr');
                emptyRow.className = 'search-empty-state';
                emptyRow.innerHTML = `
                    <td colspan="7" class="text-center py-5">
                        <div class="d-flex flex-column align-items-center">
                            <i class="bx bx-search text-muted" style="font-size: 3rem;"></i>
                            <h5 class="text-dark mt-3 mb-2">Tidak ada hasil</h5>
                            <p class="text-muted mb-0">Tidak ditemukan agen yang sesuai dengan pencarian "<strong>${searchInput.value}</strong>"</p>
                        </div>
                    </td>
                `;
                tbody.appendChild(emptyRow);
            } else {
                emptyRow.querySelector('strong').textContent = searchInput.value;
                emptyRow.style.display = '';
            }
        } else {
            // Hide search empty state
            if (emptyRow) {
                emptyRow.style.display = 'none';
            }
        }
    }

    // Function to reset all filters
    function resetAllFilters() {
        searchInput.value = '';
        filterTable();
        searchInput.focus();
    }

    // Add event listeners
    searchInput.addEventListener('input', function() {
        filterTable();
    });

    clearButton.addEventListener('click', function() {
        searchInput.value = '';
        filterTable();
        searchInput.focus();
    });

    resetButton.addEventListener('click', function() {
        resetAllFilters();
    });

    // Add keyboard shortcuts
    searchInput.addEventListener('keyup', function(e) {
        if (e.key === 'Escape') {
            this.value = '';
            filterTable();
        }
    });

    // Initialize counter on page load
    updateSearchCounter();

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
@endsection